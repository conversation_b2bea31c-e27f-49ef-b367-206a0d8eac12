//
//  ContentCell.swift
//  Shuxia<PERSON>qi
//
//  Created by yong<PERSON>ng ye on 2025/4/30.
//

import UIKit

// MARK: - ContentCell
class ContentCell: UITableViewCell {
    
    // MARK: - Properties
    
    // 内容列表容器
    private let contentContainer: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#F5F5F5")
        return view
    }()
    
    // 内容列表
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .vertical
        layout.minimumLineSpacing = 12
        layout.minimumInteritemSpacing = 12
        
        // 计算item宽度：(屏幕宽度 - 左边距20 - 右边距20 - 中间间距12) / 2
        let width = (UIScreen.main.bounds.width - 52) / 2
        layout.itemSize = CGSize(width: width, height: 380)  // 修改为实际需要的高度
        
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = UIColor(hex: "#F5F5F5")
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(ContentItemCell.self, forCellWithReuseIdentifier: "ContentItemCell")
        collectionView.contentInset = UIEdgeInsets(top: 12, left: 20, bottom: 20, right: 20)
        collectionView.isScrollEnabled = false  // 禁用滚动
        collectionView.showsVerticalScrollIndicator = false
        return collectionView
    }()
    
    // 添加数据源属性
    private var contents: [ContentItem] = []
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        backgroundColor = UIColor(hex: "#F5F5F5")
        selectionStyle = .none
        
        // 添加内容列表
        contentView.addSubview(collectionView)
        collectionView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        
        // 设置 collectionView 的背景色
        collectionView.backgroundColor = UIColor(hex: "#F5F5F5")
    }
    
    // 添加一个方法来更新数据
    func updateContents(_ items: [ContentItem]) {
        contents = items
        collectionView.reloadData()
    }
    
    // 计算所需高度的方法
    func calculateHeight() -> CGFloat {
        let itemHeight: CGFloat = 380   // 修改为实际的item高度
        let itemSpacing: CGFloat = 12   // item之间的间距
        let contentInsetsTopBottom: CGFloat = collectionView.contentInset.top + collectionView.contentInset.bottom // 上下内边距 (12 + 20 = 32)
        
        let rows = ceil(CGFloat(contents.count) / 2.0)
        // 计算 CollectionView 本身的高度
        let collectionViewHeight = (rows * itemHeight + max(0, rows - 1) * itemSpacing) + contentInsetsTopBottom
        
        // 返回 CollectionView 的高度
        return collectionViewHeight
    }
}

// MARK: - UICollectionViewDelegate & DataSource
extension ContentCell: UICollectionViewDelegate, UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return contents.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "ContentItemCell", for: indexPath) as! ContentItemCell
        let content = contents[indexPath.item]
        cell.configure(with: content)
        return cell
    }

    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        guard indexPath.item < contents.count else { return }
        let content = contents[indexPath.item]

        print("ContentCell: 点击了内容管理视频 - \(content.title), ID: \(content.id)")

        // 从 ContentItem 创建 VideoItem
        var videoItem = VideoItem()
        videoItem.id = content.id
        videoItem.worksTitle = content.title
        videoItem.worksCoverImg = content.coverImage
        videoItem.likeNumber = content.likes
        videoItem.watchNumber = content.views

        // 创建单个视频播放控制器
        let playerVC = VideoDisplayCenterViewController(
            singleVideoItem: videoItem,
            hideNavBackButton: false,
            showCustomNavBar: true,
            needsTabBarOffset: false
        )
        playerVC.hidesBottomBarWhenPushed = true

        // 获取当前的视图控制器并跳转
        if let currentVC = self.findViewController() {
            currentVC.navigationController?.pushViewController(playerVC, animated: true)
        }
    }
}
