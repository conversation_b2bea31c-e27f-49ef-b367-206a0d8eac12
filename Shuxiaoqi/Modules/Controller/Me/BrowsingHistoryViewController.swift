import UIKit
import JXSegmentedView
import SnapKit
import MJRefresh
// 浏览记录
class BrowsingHistoryViewController: BaseViewController {

    // MARK: - 数据
    // 内容浏览记录数据（示例占位，已改用 VideoItem）
    private var contentHistories: [[VideoItem]] = {
        func mock(id: String, cover: String, likes: Int) -> VideoItem {
            var item = VideoItem()
            item.videoId = id
            item.worksCoverImg = cover
            item.likeNumber = likes
            return item
        }
        return [
            [
                mock(id: "v1", cover: "collection_img1", likes: 308),
                mock(id: "v2", cover: "collection_img2", likes: 926),
                mock(id: "v3", cover: "collection_img3", likes: 521)
        ],
        [
                mock(id: "v4", cover: "collection_img4", likes: 742),
                mock(id: "v5", cover: "collection_img5", likes: 158)
        ]
    ]
    }()
    // TODO: 如果后续内容浏览记录接入分页接口，请参考 MyCollectionViewController.swift 的分页和加载更多逻辑，
    // 在接口回调处用 total 和已加载数量判断 hasMore，并设置 tableView.mj_footer 状态。

    // 商品浏览记录数据
    private var productHistories: [RecommendedGoodItem] = []

    // MARK: - 编辑模式属性 & 底部操作视图
    private var isEditingMode: Bool = false {
        didSet {
            updateEditModeUI()
            rightNavButtonTintColor = isEditingMode ? UIColor(hex: "#C4C4C4") : UIColor(hex: "#333333")
        }
    }

    private let editActionsContentHeight: CGFloat = 80
    private var selectAllButton: UIButton?
    private lazy var editActionsView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.isHidden = true

        let separator = UIView()
        separator.backgroundColor = UIColor(hex: "#EEEEEE")
        view.addSubview(separator)
        separator.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview(); make.height.equalTo(0.5)
        }

        let contentContainer = UIView(); view.addSubview(contentContainer)
        contentContainer.snp.makeConstraints { make in
            make.left.right.top.equalToSuperview(); make.bottom.equalToSuperview().offset(-WindowUtil.safeAreaBottom)
        }

        // 全选
        let selectAll = UIButton(type: .custom)
        selectAll.setImage(UIImage(named: "video_allbtn_default"), for: .normal)
        selectAll.setImage(UIImage(named: "video_allbtn_selected"), for: .selected)
        selectAll.setTitle("全选", for: .normal)
        selectAll.setTitleColor(UIColor(hex: "#333333"), for: .normal)
        selectAll.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        selectAll.imageEdgeInsets = UIEdgeInsets(top: 0, left: -4, bottom: 0, right: 8)
        selectAll.addTarget(self, action: #selector(selectAllItems(_:)), for: .touchUpInside)
        contentContainer.addSubview(selectAll)
        selectAll.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12); make.centerY.equalToSuperview(); make.height.equalTo(40)
        }
        self.selectAllButton = selectAll

        // 删除
        let delBtn = UIButton(type: .custom)
        delBtn.setTitle("删除", for: .normal)
        delBtn.setTitleColor(.white, for: .normal); delBtn.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        delBtn.backgroundColor = UIColor(hex: "#FF8F1F"); delBtn.layer.cornerRadius = 5
        delBtn.addTarget(self, action: #selector(deleteSelectedItems), for: .touchUpInside)
        contentContainer.addSubview(delBtn)
        delBtn.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-12); make.centerY.equalToSuperview(); make.width.equalTo(86); make.height.equalTo(32)
        }
        return view
    }()

    // MARK: - 组件
    private lazy var segmentedDataSource: JXSegmentedTitleDataSource = {
        let ds = JXSegmentedTitleDataSource()
        ds.titles = ["内容", "商品"]
        ds.titleSelectedColor = UIColor(hex: "#FF5936")
        ds.titleNormalColor = UIColor(hex: "#333333")
        ds.titleSelectedFont = UIFont.systemFont(ofSize: 16, weight: .medium)
        ds.titleNormalFont = UIFont.systemFont(ofSize: 16)
        ds.isTitleColorGradientEnabled = true
        ds.itemWidth = UIScreen.main.bounds.width / 2
        ds.itemSpacing = 0
        return ds
    }()

    private lazy var segmentedView: JXSegmentedView = {
        let sv = JXSegmentedView()
        sv.backgroundColor = .white
        sv.dataSource = segmentedDataSource
        let indicator = JXSegmentedIndicatorLineView()
        indicator.indicatorColor = UIColor(hex: "#FF5936")
        indicator.indicatorHeight = 2
        indicator.indicatorWidth = UIScreen.main.bounds.width / 2
        indicator.indicatorPosition = .bottom
        sv.indicators = [indicator]
        sv.delegate = self
        return sv
    }()

    private lazy var listContainerView: JXSegmentedListContainerView = {
        let container = JXSegmentedListContainerView(dataSource: self)
        return container
    }()

    // MARK: - 生命周期
    override func viewDidLoad() {
        super.viewDidLoad()
        navTitle = "浏览"
        setupNavigationBarButton()
        setupUI()

        // 将分段控制器的初始化移到这里，避免重复加载
        segmentedView.defaultSelectedIndex = 0
        segmentedView.reloadData()
    }

    // MARK: - UI
    private func setupUI() {
        // contentView 由 BaseViewController 提供
        contentView.addSubview(segmentedView)
        segmentedView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(44)
        }

        contentView.addSubview(listContainerView)
        listContainerView.snp.makeConstraints { make in
            make.top.equalTo(segmentedView.snp.bottom)
            make.left.right.bottom.equalToSuperview()
        }

        segmentedView.listContainer = listContainerView

        // 底部操作视图
        view.addSubview(editActionsView)
        view.bringSubviewToFront(editActionsView)
        // 预先设置约束，避免首次进入编辑模式时出现从顶部滑动到底部的动画
        updateEditActionsViewConstraints()
    }

    // MARK: - Navigation Button
    private func setupNavigationBarButton() {
        rightNavTitle = "管理"
        rightNavAction = #selector(toggleEditMode)
    }

    @objc private func toggleEditMode() {
        isEditingMode.toggle()
        rightNavTitle = isEditingMode ? "取消" : "管理"
        if !isEditingMode { selectAllButton?.isSelected = false }
        // 通知所有已加载列表进入/退出编辑模式
        listContainerView.validListDict.values.forEach { list in
            if let editable = list as? BaseEditModeListViewController {
                editable.setEditingMode(isEditingMode)
            }
        }
    }

    private func updateEditModeUI() {
        editActionsView.isHidden = !isEditingMode
        updateEditActionsViewConstraints()
        listContainerView.snp.remakeConstraints { make in
            make.top.equalTo(segmentedView.snp.bottom)
            make.left.right.equalToSuperview()
            if isEditingMode {
                make.bottom.equalToSuperview().offset(-editActionsContentHeight)
            } else { make.bottom.equalToSuperview() }
        }
        UIView.animate(withDuration: 0.3) { self.view.layoutIfNeeded() }
    }

    private func updateEditActionsViewConstraints() {
        let total = editActionsContentHeight + WindowUtil.safeAreaBottom
        editActionsView.snp.remakeConstraints { make in
            make.left.right.equalToSuperview(); make.height.equalTo(total); make.bottom.equalToSuperview()
        }
    }

    // MARK: - Actions
    @objc private func selectAllItems(_ sender: UIButton) {
        let target = !sender.isSelected
        let currentIdx = segmentedView.selectedIndex
        if let list = listContainerView.validListDict[currentIdx] as? BrowsingHistoryListViewController {
            list.setSelectAll(target) { allSel in sender.isSelected = allSel }
        } else if let list = listContainerView.validListDict[currentIdx] as? HistoryProductsListViewController {
            list.setSelectAll(target) { allSel in sender.isSelected = allSel }
        }
    }

    @objc private func deleteSelectedItems() {
        let currentIdx = segmentedView.selectedIndex
        if currentIdx == 0, let contentVC = listContainerView.validListDict[0] as? BrowsingHistoryListViewController {
            let ids = contentVC.selectedNumericItemIDs(); guard !ids.isEmpty else { showToast("请选择要删除的内容"); return }
            showLoadingIndicator()
            APIManager.shared.deleteUserLookByWorksIds(ids: ids) { [weak self] res in
                DispatchQueue.main.async {
                    self?.hideLoadingIndicator()
                    switch res {
                    case .success(let r) where r.isSuccess:
                        contentVC.deleteSelectedItems { updated in self?.contentHistories = updated }
                        self?.toggleEditMode()
                    case .success(let r): self?.showToast(r.displayMessage)
                    case .failure(let e): self?.showToast(e.localizedDescription)
                    }
                }
            }
        } else if currentIdx == 1, let productVC = listContainerView.validListDict[1] as? HistoryProductsListViewController {
            let ids = productVC.selectedNumericItemIDs(); guard !ids.isEmpty else { showToast("请选择要删除的商品"); return }
            showLoadingIndicator()
            APIManager.shared.deleteGoodFootRecord(ids: ids) { [weak self] res in
                DispatchQueue.main.async {
                    self?.hideLoadingIndicator()
                    switch res {
                    case .success(let r) where r.isSuccess:
                        productVC.deleteSelectedItems { updated in self?.productHistories = updated }
                        self?.toggleEditMode()
                    case .success(let r): self?.showToast(r.displayMessage)
                    case .failure(let e): self?.showToast(e.localizedDescription)
                    }
                }
            }
        }
    }
    
    // MARK: - Loading Indicator
    private func showLoadingIndicator() {
        // 实现加载指示器显示逻辑
        // 如果 BaseViewController 已有此方法，可以直接调用 super.showLoading()
        // 这里提供一个简单实现
        let loadingView = UIActivityIndicatorView(style: .medium)
        loadingView.tag = 999
        loadingView.center = view.center
        loadingView.startAnimating()
        view.addSubview(loadingView)
    }
    
    private func hideLoadingIndicator() {
        // 实现加载指示器隐藏逻辑
        // 如果 BaseViewController 已有此方法，可以直接调用 super.hideLoading()
        view.viewWithTag(999)?.removeFromSuperview()
    }

    // MARK: - 空数据占位视图 (moved)
    //    private lazy var emptyPlaceholderView: UIView = {
    //        let container = UIView()
    //        let imageView = UIImageView()
    //        imageView.contentMode = .scaleAspectFit
    //        // 使用项目中的占位图片名，如果没有可替换为系统图标
    //        imageView.image = UIImage(named: "empty_placeholder") ?? UIImage(systemName: "tray")
    //        container.addSubview(imageView)
    //        imageView.snp.makeConstraints { make in
    //            make.centerX.equalToSuperview(); make.top.equalToSuperview()
    //            make.width.height.equalTo(120)
    //        }
    //        let label = UILabel()
    //        label.text = "暂无浏览记录"
    //        label.textColor = UIColor(hex: "#999999")
    //        label.font = UIFont.systemFont(ofSize: 14)
    //        container.addSubview(label)
    //        label.snp.makeConstraints { make in
    //            make.top.equalTo(imageView.snp.bottom).offset(12)
    //            make.centerX.equalToSuperview()
    //        }
    //        return container
    //    }()
    //    
    //    // 更新占位视图显示状态
    //    private func updatePlaceholderVisibility() {
    //        // 已迁移到 BrowsingHistoryListViewController
    //    }
}

// MARK: - JXSegmentedViewDelegate
extension BrowsingHistoryViewController: JXSegmentedViewDelegate {}

// MARK: - JXSegmentedListContainerViewDataSource
extension BrowsingHistoryViewController: JXSegmentedListContainerViewDataSource {
    func numberOfLists(in listContainerView: JXSegmentedListContainerView) -> Int {
        return 2
    }

    func listContainerView(_ listContainerView: JXSegmentedListContainerView, initListAt index: Int) -> JXSegmentedListContainerViewListDelegate {
        if index == 0 {
            // 内容列表，使用专门的浏览记录列表控制器
            let browsingVC = BrowsingHistoryListViewController(sectionTitles: ["最近", "更早"], collections: contentHistories) { [weak self] updated in
                self?.contentHistories = updated
            }
            // 设置选中状态变化回调
            browsingVC.onSelectionChange = { [weak self] hasSelection, allSelected in
                guard let self = self else { return }
                self.selectAllButton?.isSelected = allSelected
            }
            return browsingVC
        } else {
            // 商品列表
            let productsVC = HistoryProductsListViewController(products: productHistories)
            productsVC.onSelectionChange = { [weak self] hasSel, allSel in
                self?.selectAllButton?.isSelected = allSel
            }
            return productsVC
        }
    }
}

// MARK: - 浏览记录列表控制器
class BrowsingHistoryListViewController: BaseEditModeListViewController {
    
    // 表格视图
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .grouped)
        tableView.delegate = self
        tableView.dataSource = self
        tableView.backgroundColor = UIColor(hex: "#F5F5F5")
        tableView.separatorStyle = .none
        tableView.showsVerticalScrollIndicator = false
        tableView.register(ContentCollectionCell.self, forCellReuseIdentifier: "ContentCollectionCell")
        tableView.register(CollectionSectionHeaderView.self, forHeaderFooterViewReuseIdentifier: "CollectionSectionHeaderView")
        tableView.contentInset = UIEdgeInsets(top: 10, left: 0, bottom: 20, right: 0)
        tableView.allowsMultipleSelectionDuringEditing = true
        return tableView
    }()
    
    // 分区标题
    private let sectionTitles: [String]
    
    // 浏览记录数据
    private var collections: [[VideoItem]]
    // 已选中的作品ID集合
    private var selectedIds: Set<String> = []
    // 数据更新闭包，当选中状态或数据删除时回调
    private var onDataUpdate: (([[VideoItem]]) -> Void)?
    // 选中状态变化回调 (是否有选中项, 是否全部选中)
    var onSelectionChange: ((_ hasSelection: Bool, _ allSelected: Bool) -> Void)?
    
    // 记录原始底部inset
    private var originalBottomInset: CGFloat = 0
    
    // MARK: 分页属性
    private var currentPage: Int = 0
    private var hasMore: Bool = true
    private var isLoading: Bool = false

    init(sectionTitles: [String], collections: [[VideoItem]], onDataUpdate: (([[VideoItem]]) -> Void)? = nil) {
        self.sectionTitles = sectionTitles
        self.collections = collections
        self.onDataUpdate = onDataUpdate
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        // 记录初始inset
        originalBottomInset = tableView.contentInset.bottom
        setupRefresh()
        fetchBrowsingHistory(isRefresh: true)
    }
    
    private func setupUI() {
        view.addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        // 空数据占位图
        view.addSubview(emptyPlaceholderView)
        emptyPlaceholderView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalTo(200)
            make.height.equalTo(200)
        }
        emptyPlaceholderView.isHidden = true
    }
    
    // 重写基类方法以更新 TableView
    override func updateUIForEditingMode(_ isEditing: Bool) {
        super.updateUIForEditingMode(isEditing)
        // 更新底部 inset，但无需触发表格整体刷新
        tableView.contentInset.bottom = isEditing ? (originalBottomInset + WindowUtil.safeAreaBottom) : originalBottomInset
        
        if !isEditing {
            // 退出编辑模式，清空选中集合
            selectedIds.removeAll()
            updateAndNotifySelectionState()
        } else {
            // 进入编辑模式，确保初始状态被通知
            updateAndNotifySelectionState()
        }
        // 局部刷新当前可见 Cell，避免闪烁与额外网络请求
        refreshVisibleCellsEditingState()
        
        // 避免编辑模式切换引发 MJRefresh 自动加载造成多余接口请求
        if isEditing {
            // 隐藏 footer，防止触发 loadMore
            tableView.mj_footer?.isHidden = true
            tableView.mj_footer?.endRefreshing()
        } else {
            // 恢复 footer 显示（如果还有更多数据）
            tableView.mj_footer?.isHidden = !hasMore
        }
    }
    
    /// 刷新当前可见 Cell 的编辑状态和选中指示器，避免整表 reload
    private func refreshVisibleCellsEditingState() {
        for case let cell as ContentCollectionCell in tableView.visibleCells {
            cell.updateEditing(isListEditing, selectedIds: selectedIds)
        }
    }
    
    // MARK: - 处理单个 item 选中切换
    func toggleItemSelection(at indexPath: IndexPath, itemIndex: Int) {
        guard isListEditing else { return }
        guard indexPath.section < collections.count, itemIndex < collections[indexPath.section].count else { return }
        if let wid = collections[indexPath.section][itemIndex].worksIdString {
            if selectedIds.contains(wid) {
                selectedIds.remove(wid)
            } else {
                selectedIds.insert(wid)
            }
        }
        if let cell = tableView.cellForRow(at: indexPath) as? ContentCollectionCell {
            cell.updateItemSelectionState(at: itemIndex, isSelected: selectedIds)
        }
        updateAndNotifySelectionState()
    }
    
    // MARK: - 删除选中项
    func deleteSelectedItems(completion: @escaping ([[VideoItem]]) -> Void) {
        guard isListEditing else { return }
        var updated: [[VideoItem]] = []
        var didDelete = false
        for section in collections {
            let remain = section.filter { item in
                guard let wid = item.worksIdString else { return true }
                return !selectedIds.contains(wid)
            }
            if remain.count != section.count { didDelete = true }
            updated.append(remain)
        }
        guard didDelete else { return }
        collections = updated
        onDataUpdate?(updated)
        tableView.reloadData()
        updateAndNotifySelectionState()
        completion(updated)

        // 删除后更新占位视图
        updatePlaceholderVisibility()
    }
    
    // MARK: - 计算并通知选中状态
    private func calculateSelectionState() -> (hasSelection: Bool, allSelected: Bool) {
        let total = collections.reduce(0) { $0 + $1.count }
        let selected = selectedIds.count
        let hasSelection = selected > 0
        let allSelected = total > 0 && selected == total
        return (hasSelection, allSelected)
    }
    
    func areAllItemsSelected() -> Bool {
        return calculateSelectionState().allSelected
    }
    
    func updateAndNotifySelectionState() {
        guard isListEditing else { return }
        let state = calculateSelectionState()
        onSelectionChange?(state.hasSelection, state.allSelected)
    }
    
    // MARK: - 网络加载浏览记录
    private func setupRefresh() {
        // 下拉刷新
        let header = MJRefreshNormalHeader(refreshingTarget: self, refreshingAction: #selector(refreshData))
        // 让刷新提示整体向上偏移，避免被顶部分段控制器遮挡
        header.ignoredScrollViewContentInsetTop = 44 // 与分段控制器高度一致，刷新提示隐藏在其后
        tableView.mj_header = header
        // 上拉加载更多
        tableView.mj_footer = MJRefreshAutoNormalFooter(refreshingTarget: self, refreshingAction: #selector(loadMore))
    }
    
    @objc private func refreshData() {
        currentPage = 0
        fetchBrowsingHistory(isRefresh: true)
    }
    
    @objc private func loadMore() {
        guard hasMore, !isLoading else {
            tableView.mj_footer?.endRefreshing()
            return
        }
        currentPage += 1
        fetchBrowsingHistory(isRefresh: false)
    }
    
    private func fetchBrowsingHistory(isRefresh: Bool) {
        isLoading = true
        // 使用正确的浏览记录API
        APIManager.shared.getUserWorksLook(page: currentPage, size: 100) { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                self.isLoading = false
                self.tableView.mj_header?.endRefreshing()
                self.tableView.mj_footer?.endRefreshing()
                
                switch result {
                case .success(let response):
                    guard response.isSuccess, let list = response.data?.list else { return }
                    let items: [VideoItem] = list
                    
                    if isRefresh {
                        // 下拉刷新：替换数据并清空选中状态
                        self.collections = [items]
                        self.selectedIds.removeAll()
                    } else {
                        // 加载更多：追加到现有数据末尾
                        if self.collections.isEmpty { self.collections.append([]) }
                        self.collections[0].append(contentsOf: items)
                    }
                    
                    // 更新hasMore状态
                    if let total = response.data?.total {
                        let loadedCount = self.collections.flatMap { $0 }.count
                        self.hasMore = loadedCount < total
                    } else {
                        self.hasMore = response.data?.hasMore ?? false
                    }
                    
                    // 更新footer状态
                    if self.hasMore {
                        self.tableView.mj_footer?.isHidden = false
                        self.tableView.mj_footer?.endRefreshing()
                    } else {
                        self.tableView.mj_footer?.isHidden = true
                    }
                    
                    // 通知外部数据已更新
                    self.onDataUpdate?(self.collections)
                    self.updatePlaceholderVisibility()
                    self.tableView.reloadData()
                    
                case .failure(let error):
                    print("浏览记录请求失败: \(error)")
                }
            }
        }
    }
    
    // MARK: - 对外公开：获取已选中的作品ID，用于网络删除
    func selectedItemIDs() -> [String] {
        return Array(selectedIds)
    }
    
    // MARK: - 获取用于API调用的纯数字ID列表，返回Int类型
    func selectedNumericItemIDs() -> [Int] {
        var numericIds: [Int] = []
        
        // 遍历所有选中项
        for section in collections {
            for item in section {
                if let id = item.worksIdString, selectedIds.contains(id) {
                    // 优先使用整型ID
                    if let intId = item.id {
                        numericIds.append(intId)
                    }
                    // 如果没有整型ID，尝试将videoId转换为Int
                    else if let vid = item.videoId, let intValue = Int(vid) {
                        numericIds.append(intValue)
                    }
                }
            }
        }
        
        return numericIds
    }

    // 公开方法：设置全选/取消全选
    func setSelectAll(_ selectAll: Bool, completion: ((Bool) -> Void)? = nil) {
        // 收集所有 id
        var allIds: [String] = []
        for section in collections {
            for v in section {
                if let wid = v.worksIdString { allIds.append(wid) }
            }
        }
        if selectAll {
            selectedIds = Set(allIds)
        } else {
            selectedIds.removeAll()
        }
        refreshVisibleCellsEditingState()
        updateAndNotifySelectionState()
        completion?(selectAll)
    }

    // MARK: - 空数据占位视图 (migrated from parent controller)
    private lazy var emptyPlaceholderView: UIView = {
        let container = UIView()
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.image = UIImage(named: "empty_data_placeholder_image") ?? UIImage(systemName: "tray")
        container.addSubview(imageView)
        imageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview()
            make.width.height.equalTo(120)
        }
        let label = UILabel()
        label.text = "暂无浏览记录"
        label.textColor = UIColor(hex: "#999999")
        label.font = UIFont.systemFont(ofSize: 14)
        container.addSubview(label)
        label.snp.makeConstraints { make in
            make.top.equalTo(imageView.snp.bottom).offset(12)
            make.centerX.equalToSuperview()
        }
        return container
    }()

    // 更新占位视图显示状态
    private func updatePlaceholderVisibility() {
        // 判断所有分区是否均为空
        let hasData = collections.contains { !$0.isEmpty }
        emptyPlaceholderView.isHidden = hasData
        tableView.isHidden = !hasData && !isListEditing // 如果无数据且非编辑状态，隐藏表格
    }

    // MARK: - 视频播放
    private func playVideo(sectionIndex: Int, itemIndex: Int) {
        // 平铺所有 items
        let flatList = collections.flatMap { $0 }
        let startFlatIndex = collections[..<sectionIndex].reduce(0) { $0 + $1.count } + itemIndex
        guard startFlatIndex < flatList.count else { return }

        let playerVC = VideoDisplayCenterViewController(
            videoList: flatList,
            startIndex: startFlatIndex,
            hideNavBackButton: false,
            showCustomNavBar: true,
            needsTabBarOffset: false
        )
        playerVC.hidesBottomBarWhenPushed = true
        if let nav = self.navigationController {
            nav.pushViewController(playerVC, animated: true)
        } else if let root = UIApplication.shared.windows.first(where: { $0.isKeyWindow })?.rootViewController {
            root.present(playerVC, animated: true)
        }
    }
}

// MARK: - UITableViewDelegate, UITableViewDataSource
extension BrowsingHistoryListViewController: UITableViewDelegate, UITableViewDataSource {
    
    func numberOfSections(in tableView: UITableView) -> Int {
        return collections.count
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        // 如果分区为空，则不显示该行 (避免空行)
        return collections[section].isEmpty ? 0 : 1
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "ContentCollectionCell", for: indexPath) as! ContentCollectionCell
        let items = collections[indexPath.section]
        // 确保传递正确的 selection handler
        cell.configure(with: items, selectedIds: selectedIds, isEditing: isListEditing) { [weak self] itemIndex in
            guard let self = self else { return }
            if self.isListEditing {
                // 编辑模式：切换选择状态
                self.toggleItemSelection(at: indexPath, itemIndex: itemIndex)
            } else {
                // 非编辑模式：播放视频
                self.playVideo(sectionIndex: indexPath.section, itemIndex: itemIndex)
            }
        }
        return cell
    }
    
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        // 如果分区为空，不显示 Header
        guard !collections[section].isEmpty else { return nil }
        
        let headerView = tableView.dequeueReusableHeaderFooterView(withIdentifier: "CollectionSectionHeaderView") as! CollectionSectionHeaderView
        if section < sectionTitles.count {
            headerView.configure(with: sectionTitles[section])
        }
        return headerView
    }
    
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        // 如果分区为空，Header 高度为 0
        return collections[section].isEmpty ? 0.1 : 40 // 使用 0.1 避免约束问题
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        // 如果分区为空，行高为0
        guard !collections[indexPath.section].isEmpty else { return 0 }
        
        let items = collections[indexPath.section]
        let itemWidth = (UIScreen.main.bounds.width - 48) / 3
        let itemHeight = itemWidth * 1.5
        let rows = ceil(Double(items.count) / 3.0)
        let totalHeight = CGFloat(rows) * itemHeight + max(0, CGFloat(rows - 1)) * 8 // 行间距
        return totalHeight
    }
}

// MARK: - 商品浏览记录列表（复用商品样式）
class HistoryProductsListViewController: BaseEditModeListViewController {

    // MARK: 分页属性
    private var currentPage: Int = 0
    private var hasMore: Bool = true
    private var isLoading: Bool = false

    private var products: [RecommendedGoodItem]

    // 选中集合
    private var selectedIds: Set<Int> = []
    // 回调
    var onSelectionChange: ((_ hasSelection: Bool, _ allSelected: Bool) -> Void)?
    // 原始bottom inset
    private var originalBottomInset: CGFloat = 0

    init(products: [RecommendedGoodItem]) {
        self.products = products
        super.init(nibName: nil, bundle: nil)
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - UI
    // 占位图
    private lazy var emptyPlaceholderView: UIView = {
        let container = UIView()
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.image = UIImage(named: "empty_data_placeholder_image") ?? UIImage(systemName: "tray")
        container.addSubview(imageView)
        imageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview(); make.top.equalToSuperview(); make.width.height.equalTo(120)
        }
        let label = UILabel()
        label.text = "暂无浏览记录"
        label.textColor = UIColor(hex: "#999999"); label.font = UIFont.systemFont(ofSize: 14)
        container.addSubview(label)
        label.snp.makeConstraints { make in
            make.top.equalTo(imageView.snp.bottom).offset(12); make.centerX.equalToSuperview()
        }
        return container
    }()

    // UICollectionView
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.minimumInteritemSpacing = 8
        layout.minimumLineSpacing = 10
        let itemWidth = (UIScreen.main.bounds.width - 12 * 2 - 8) / 2
        layout.itemSize = CGSize(width: itemWidth, height: 270)
        layout.sectionInset = UIEdgeInsets(top: 0, left: 12, bottom: 12, right: 12)

        let cv = UICollectionView(frame: .zero, collectionViewLayout: layout)
        cv.backgroundColor = UIColor(hex: "#F5F5F5")
        cv.delegate = self
        cv.dataSource = self
        cv.register(DiscoverProductCell.self, forCellWithReuseIdentifier: "DiscoverProductCell")
        return cv
    }()

    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = UIColor(hex: "#F5F5F5")
        view.addSubview(collectionView)
        collectionView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        // 占位图
        view.addSubview(emptyPlaceholderView)
        emptyPlaceholderView.snp.makeConstraints { make in
            make.center.equalToSuperview(); make.width.equalTo(200); make.height.equalTo(200)
        }
        emptyPlaceholderView.isHidden = true

        setupRefresh()
        fetchGoodsHistory(isRefresh: true)
    }

    // MARK: - Refresh & Pagination
    private func setupRefresh() {
        let header = MJRefreshNormalHeader(refreshingTarget: self, refreshingAction: #selector(refreshData))
        collectionView.mj_header = header
        let footer = MJRefreshAutoNormalFooter(refreshingTarget: self, refreshingAction: #selector(loadMore))
        collectionView.mj_footer = footer
    }

    @objc private func refreshData() {
        currentPage = 0
        fetchGoodsHistory(isRefresh: true)
    }

    @objc private func loadMore() {
        guard hasMore, !isLoading else {
            collectionView.mj_footer?.endRefreshing(); return
        }
        currentPage += 1
        fetchGoodsHistory(isRefresh: false)
    }

    private func fetchGoodsHistory(isRefresh: Bool) {
        isLoading = true
        APIManager.shared.getGoodsFoot(page: currentPage, size: 10) { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                self.isLoading = false
                self.collectionView.mj_header?.endRefreshing()
                self.collectionView.mj_footer?.endRefreshing()
                switch result {
                case .success(let resp):
                    if resp.status == 1 {
                        let rawList = resp.data.items
                        let list = rawList.map { item -> RecommendedGoodItem in
                            var g = RecommendedGoodItem()
                            g.goodId = item.goodId ?? Int(item.goodsId ?? "0")
                            g.goodName = item.goodName
                            g.swiperImageList = item.swiperImageList
                            g.sales = item.sales
                            g.goodPriceRange = item.goodPriceRange
                            return g
                        }
                        if isRefresh { self.products = list } else { self.products.append(contentsOf: list) }
                        let total = resp.data.totalCount
                        self.hasMore = self.products.count < total
                        self.collectionView.mj_footer?.isHidden = !self.hasMore
                        self.collectionView.reloadData()
                    }
                case .failure(let error):
                    print("商品浏览记录请求失败: \(error)")
                }
                self.updatePlaceholderVisibility()
            }
        }
    }

    private func updatePlaceholderVisibility() {
        let hasData = !products.isEmpty
        emptyPlaceholderView.isHidden = hasData
        collectionView.isHidden = !hasData
    }

    // MARK: - 编辑模式覆盖
    override func updateUIForEditingMode(_ isEditing: Bool) {
        super.updateUIForEditingMode(isEditing)
        collectionView.allowsMultipleSelection = true
        collectionView.contentInset.bottom = isEditing ? (originalBottomInset + WindowUtil.safeAreaBottom) : originalBottomInset
        refreshVisibleCellsEditingState()
    }

    private func refreshVisibleCellsEditingState() {
        for case let cell as DiscoverProductCell in collectionView.visibleCells {
            let gid = cell.tag // we'll set tag later when configuring
            let isSel = selectedIds.contains(gid)
            cell.updateEditing(isListEditing, isSelected: isSel)
        }
    }

    func setSelectAll(_ selectAll: Bool, completion: ((Bool)->Void)? = nil) {
        let allIds = products.compactMap { $0.goodId }
        if selectAll { selectedIds = Set(allIds) } else { selectedIds.removeAll() }
        collectionView.reloadData()
        updateAndNotifySelectionState()
        completion?(selectAll)
    }

    func areAllItemsSelected() -> Bool { return selectedIds.count == products.count && !products.isEmpty }

    func updateAndNotifySelectionState() {
        guard isListEditing else { return }
        let hasSel = !selectedIds.isEmpty
        onSelectionChange?(hasSel, areAllItemsSelected())
    }

    func selectedNumericItemIDs() -> [Int] { return Array(selectedIds) }

    func deleteSelectedItems(completion: @escaping ([RecommendedGoodItem])->Void) {
        guard isListEditing else { return }
        products.removeAll { item in
            if let id = item.goodId { return selectedIds.contains(id) }
            return false
        }
        selectedIds.removeAll()
        collectionView.reloadData()
        updatePlaceholderVisibility()
        completion(products)
    }
}

extension HistoryProductsListViewController: UICollectionViewDataSource, UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return products.count
    }
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "DiscoverProductCell", for: indexPath) as! DiscoverProductCell
        cell.configureWithRecommendedItem(products[indexPath.item])
        cell.tag = products[indexPath.item].goodId ?? 0 // Set tag for selection
        return cell
    }
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        if isListEditing {
            guard indexPath.item < products.count else { return }
            if let gid = products[indexPath.item].goodId {
                if selectedIds.contains(gid) { selectedIds.remove(gid) } else { selectedIds.insert(gid) }
            }
            if let cell = collectionView.cellForItem(at: indexPath) as? DiscoverProductCell {
                let isSel: Bool
                if let id = products[indexPath.item].goodId { isSel = selectedIds.contains(id) } else { isSel = false }
                cell.updateEditing(isListEditing, isSelected: isSel)
            }
            updateAndNotifySelectionState()
        } else {
            // TODO: 跳转到商品详情
        }
    }
}

// MARK: - Network Extensions for Browsing History Deletion
extension APIRouter.Center {
    static func deleteUserLookByWorksIds(ids: [Int]) -> APIRequest {
        APIRequest(path: "/api/video/center/deleteUserLookByWorksIds", method: .post, parameters: ["ids": ids]
        )
    }
    
    static func getUserWorksLook(page: Int, size: Int) -> APIRequest {
        APIRequest(
            path: "/api/video/center/pageUserWorksLook",
            method: .get,
            parameters: ["page": page, "size": size]
        )
    }
}

extension APIManager {
    func deleteUserLookByWorksIds(ids: [Int], completion: @escaping (Result<BaseResponse, APIError>) -> Void) {
        let req = APIRouter.Center.deleteUserLookByWorksIds(ids: ids)
        APIService.shared.request(req, completion: completion)
    }
    
    func getUserWorksLook(page: Int, size: Int, completion: @escaping (Result<VideoCollectionListResponse, APIError>) -> Void) {
        let req = APIRouter.Center.getUserWorksLook(page: page, size: size)
        APIService.shared.request(req, completion: completion)
    }
} 

// MARK: - Network Extensions for Goods Foot
extension APIRouter {
    enum Goods {
        static func goodsFoot(page: Int, size: Int) -> APIRequest {
            APIRequest(path: "/api/goods/goodsFoot", method: .get, parameters: ["page": page, "size": size])
        }
        
        // 删除商品足迹
        static func deleteGoodFootRecord(ids: [Int]) -> APIRequest {
            APIRequest(
                path: "/api/goods/v1/deleteGoodFootRecord",
                method: .post,
                parameters: ["ids": ids]
            )
        }
    }
}

extension APIManager {
    func getGoodsFoot(page: Int, size: Int, completion: @escaping (Result<EcommerceProductSearchResponse, APIError>) -> Void) {
        let req = APIRouter.Goods.goodsFoot(page: page, size: size)
        APIService.shared.request(req, completion: completion)
    }
    
    // 删除商品浏览记录
    func deleteGoodFootRecord(ids: [Int], completion: @escaping (Result<BaseResponse, APIError>) -> Void) {
        // 后端接口接收数组
        let req = APIRouter.Goods.deleteGoodFootRecord(ids: ids)
        APIService.shared.request(req, completion: completion)
    }
}
