//
//  LikedWorksViewController.swift
//  Shu<PERSON><PERSON>qi
//
//  Created by yongsheng ye on 2025/5/26.
//
//  用户点赞过的视频列表

import Foundation
import UIKit
import JXSegmentedView
import SnapKit
import MBProgressHUD

// MARK: - 日期格式化工具
class DateFormatterUtil {
    static let shared = DateFormatterUtil()
    private let calendar = Calendar.current
    
    private init() {}
    
    func formatDate(_ date: Date) -> String {
        let now = Date()
        let components = calendar.dateComponents([.day], from: date, to: now)
        
        // 获取日期间隔天数
        if let days = components.day {
            switch days {
            case 0:
                return "今天"
            case 1:
                return "昨天"
            case 2:
                return "前天"
            default:
                // 判断是否是今年
                let yearComponents = calendar.dateComponents([.year], from: date, to: now)
                if let yearDiff = yearComponents.year, yearDiff == 0 {
                    // 今年，只显示月日
                    let formatter = DateFormatter()
                    formatter.dateFormat = "MM月dd日"
                    return formatter.string(from: date)
                } else {
                    // 去年或更早，显示完整日期
                    let formatter = DateFormatter()
                    formatter.dateFormat = "yyyy年MM月dd日"
                    return formatter.string(from: date)
                }
            }
        }
        return ""
    }
}

class LikedWorksViewController: BaseViewController {
    
    // MARK: - 属性
    
    // 是否处于编辑模式
    private var isEditingMode: Bool = false {
        didSet {
            updateEditModeUI()
            notifyChildViewControllersEditModeChanged()
            rightNavButtonTintColor = isEditingMode ? UIColor(hex: "#C4C4C4") : UIColor(hex: "#333333")
        }
    }
    
    // 点赞内容数据
    private var likedVideos: [[LikedVideoItem]] = []
    
    // 分区标题（动态生成）
    private var sectionTitles: [String] = []
    
    // 用于持有全选按钮的引用
    private var selectAllButton: UIButton?
    
    // 分页相关属性
    private var currentPage: Int = 0
    private var isLoading: Bool = false
    private var hasMoreData: Bool = true
    
    // 空状态视图
    private lazy var emptyStateView: EmptyStateView = {
        let view = EmptyStateView()
        view.configure(
            image: UIImage(named: "empty_data_placeholder_image") ?? UIImage(),
            title: "暂无点赞内容",
            subtitle: "快去发现更多精彩视频吧"
        )
        view.isHidden = true
        return view
    }()
    
    // 列表容器
    private lazy var listContainerView: LikedVideosListView = {
        let containerView = LikedVideosListView(sectionTitles: sectionTitles, videos: likedVideos) { [weak self] updatedVideos in
            self?.likedVideos = updatedVideos
        }
        containerView.onSelectionChange = { [weak self] hasSelection, allSelected in
            guard let self = self, let button = self.selectAllButton else { return }
            button.isSelected = allSelected
        }
        // 设置视频播放回调
        containerView.onVideoPlay = { [weak self] sectionIndex, itemIndex in
            self?.playVideo(sectionIndex: sectionIndex, itemIndex: itemIndex)
        }
        // 下拉刷新
        containerView.onPullToRefresh = { [weak self] in
            self?.refreshLikedVideos()
        }
        // 上拉加载更多
        containerView.onLoadMore = { [weak self] in
            self?.loadMoreLikedVideos()
        }
        return containerView
    }()
    
    // 底部编辑操作视图
    private let editActionsContentHeight: CGFloat = 80
    private lazy var editActionsView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.isHidden = true // 默认隐藏

        // 添加分割线
        let separator = UIView()
        separator.backgroundColor = UIColor(hex: "#EEEEEE")
        view.addSubview(separator)
        separator.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(0.5)
        }

        // 内容容器，保证内容不会被安全区遮挡
        let contentContainer = UIView()
        contentContainer.backgroundColor = .clear
        view.addSubview(contentContainer)
        contentContainer.snp.makeConstraints { make in
            make.left.right.top.equalToSuperview()
            make.bottom.equalToSuperview().offset(-WindowUtil.safeAreaBottom)
        }

        // 添加全选按钮
        let selectAllBtn = UIButton(type: .custom)
        // 设置不同状态的图片，避免系统默认在 selected 状态下添加背景色
        selectAllBtn.setImage(UIImage(named: "video_allbtn_default"), for: .normal)
        selectAllBtn.setImage(UIImage(named: "video_allbtn_selected"), for: .selected)
        selectAllBtn.setTitle("全选", for: .normal)
        selectAllBtn.setTitleColor(UIColor(hex: "#333333"), for: .normal)
        selectAllBtn.titleLabel?.font = UIFont.systemFont(ofSize: 14)
        // 调整图片和文字的间距，使其与原设计保持一致
        selectAllBtn.imageEdgeInsets = UIEdgeInsets(top: 0, left: -4, bottom: 0, right: 8)
        selectAllBtn.contentEdgeInsets = UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 8)
        // 关闭高亮时的系统灰色背景
        selectAllBtn.adjustsImageWhenHighlighted = false
        selectAllBtn.addTarget(self, action: #selector(selectAllItems(_:)), for: .touchUpInside)
        contentContainer.addSubview(selectAllBtn)
        selectAllBtn.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
            make.height.equalTo(40)
        }
        self.selectAllButton = selectAllBtn

        // 添加删除按钮
        let deleteBtn = UIButton(type: .custom)
        deleteBtn.setTitle("取消点赞", for: .normal)
        deleteBtn.setTitleColor(.white, for: .normal)
        deleteBtn.titleLabel?.font = UIFont.systemFont(ofSize: 14, weight: .medium)
        deleteBtn.backgroundColor = UIColor(hex: "#FF8F1F")
        deleteBtn.layer.cornerRadius = 5
        deleteBtn.addTarget(self, action: #selector(deleteSelectedItems), for: .touchUpInside)
        contentContainer.addSubview(deleteBtn)
        deleteBtn.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-12)
            make.centerY.equalToSuperview()
            make.width.equalTo(86)
            make.height.equalTo(32)
        }

        return view
    }()
    
    // MARK: - 生命周期
    
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .white
        navTitle = "点赞"

        // 设置自定义返回按钮
        let backButton = UIBarButtonItem(image: UIImage(named: "nav_back"), style: .plain, target: self, action: #selector(customBackAction))
        navigationItem.leftBarButtonItem = backButton

        setupNavigationBarButton()
        setupUI()

        // 请求点赞列表数据
        requestLikedVideos(reset: true)
    }



    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        updateEditActionsViewConstraints()
    }
    
    @objc private func customBackAction() {
        if isEditingMode {
            // 如果处于管理模式，先退出管理模式
            isEditingMode = false
            rightNavTitle = "管理"
            rightNavButtonTintColor = UIColor(hex: "#333333")
            // 可选：给用户提示
        } else {
            // 非管理模式，正常返回
            navigationController?.popViewController(animated: true)
        }
    }
    
    // MARK: - 网络请求
    
    private func refreshLikedVideos() {
        requestLikedVideos(reset: true)
    }
    
    private func loadMoreLikedVideos() {
        guard !isLoading, hasMoreData else { return }
        requestLikedVideos(reset: false)
    }
    
    private func requestLikedVideos(reset: Bool = false) {
        if reset {
            currentPage = 0
            hasMoreData = true
        }
        guard hasMoreData else {
            listContainerView.endRefreshing()
            return
        }
        isLoading = true
        if reset {
            showLoading()
        }
        APIManager.shared.getVideoLikesList(page: currentPage, size: 20) { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                self.hideLoading()
                self.isLoading = false
                self.listContainerView.endRefreshing()
                switch result {
                case .success(let response):
                    //打印条数
                    print("response.data?.list.count: \(response.data?.list?.count ?? 0)")

                    // 将空数组与 nil 统一处理，避免空态无法触发
                    let videoItems = response.data?.list ?? []

                    let likedItems = videoItems.compactMap { videoItem -> LikedVideoItem? in
                        guard videoItem.deleted == 0 else { return nil }
                        let date = self.convertStringToDate(videoItem.createTime)
                        let isValid = (videoItem.createTime != nil && videoItem.worksTitle != nil)
                        return LikedVideoItem(
                            image: videoItem.worksCoverImg ?? "",
                            likeCount: videoItem.likeNumber ?? 0,
                            date: date,
                            videoId: videoItem.id.map { String($0) } ?? "",
                            title: videoItem.worksTitle ?? "",
                            description: videoItem.worksDescribe ?? "",
                            isValid: isValid
                        )
                    }

                    if reset {
                        self.updateVideosData(likedItems)
                    } else {
                        // 追加数据
                        var allItems = self.likedVideos.flatMap { $0 }
                        allItems.append(contentsOf: likedItems)
                        self.updateVideosData(allItems)
                    }

                    // 判断是否还有更多
                    if let total = response.data?.total {
                        let loadedCount = self.likedVideos.flatMap { $0 }.count
                        self.hasMoreData = loadedCount < total
                    } else {
                        self.hasMoreData = (videoItems.count >= 20)
                    }

                    // 根据 hasMoreData 更新 footer 显示
                    let tableView = self.listContainerView.mainTableView
                    tableView.mj_footer?.isHidden = !self.hasMoreData
                    if self.hasMoreData {
                        tableView.mj_footer?.endRefreshing()
                    }

                    if self.hasMoreData {
                        self.currentPage += 1
                    }
                case .failure(let error):
                    self.showToast(error.localizedDescription)
                    // 请求失败时，如果是首次加载（重置），显示空状态
                    if reset {
                        self.emptyStateView.isHidden = false
                        self.navigationItem.rightBarButtonItem?.isEnabled = false
                    }
                }
            }
        }
    }
    
    // MARK: - 日期字符串转Date
    private func convertStringToDate(_ dateString: String?) -> Date? {
        guard let dateString = dateString else { return nil }
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "zh_CN")
        formatter.timeZone = TimeZone.current
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        return formatter.date(from: dateString)
    }
    
    // MARK: - 数据处理
    
    private func updateVideosData(_ items: [LikedVideoItem]) {
        // 按日期对视频进行分组
        var groupedVideos: [Date: [LikedVideoItem]] = [:]
        var unknownVideos: [LikedVideoItem] = []

        for video in items {
            if let date = video.date {
                let calendar = Calendar.current
                let components = calendar.dateComponents([.year, .month, .day], from: date)
                if let dayDate = calendar.date(from: components) {
                    if groupedVideos[dayDate] == nil {
                        groupedVideos[dayDate] = []
                    }
                    groupedVideos[dayDate]?.append(video)
                }
            } else {
                // 时间未知，归入未知分组
                unknownVideos.append(video)
            }
        }

        // 按日期排序
        let sortedDates = groupedVideos.keys.sorted(by: >)

        // 更新数据源和标题
        var newVideos: [[LikedVideoItem]] = []
        var newTitles: [String] = []

        for date in sortedDates {
            if let videos = groupedVideos[date] {
                newVideos.append(videos)
                newTitles.append(DateFormatterUtil.shared.formatDate(date))
            }
        }

        // 最后添加未知分组
        if !unknownVideos.isEmpty {
            newVideos.append(unknownVideos)
            newTitles.append("未知")
        }

        // 更新数据
        likedVideos = newVideos
        sectionTitles = newTitles

        // 更新列表视图
        listContainerView.updateData(videos: likedVideos, sectionTitles: sectionTitles)
        
        // 更新空状态视图显示
        let totalItems = items.count
        emptyStateView.isHidden = totalItems > 0
        
        // 如果没有数据，隐藏管理按钮
        navigationItem.rightBarButtonItem?.isEnabled = totalItems > 0
    }
    
    // MARK: - UI 设置
    
    private func setupNavigationBarButton() {
        rightNavTitle = "管理"
        rightNavAction = #selector(toggleEditMode)
        // 初始时禁用管理按钮，直到有数据加载
        navigationItem.rightBarButtonItem?.isEnabled = false
    }
    
    private func setupUI() {
        // 添加列表视图
        contentView.addSubview(listContainerView)
        listContainerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 添加底部操作视图
        view.addSubview(editActionsView)
        view.bringSubviewToFront(editActionsView)
        
        // 添加空状态视图
        contentView.addSubview(emptyStateView)
        emptyStateView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    // MARK: - 编辑模式逻辑
    
    @objc private func toggleEditMode() {
        isEditingMode.toggle()
        
        rightNavButtonTintColor = isEditingMode ? UIColor(hex: "#C4C4C4") : UIColor(hex: "#333333")
        rightNavTitle = isEditingMode ? "取消" : "管理"
        
        if !isEditingMode {
            selectAllButton?.isSelected = false
        }
    }
    
    private func updateEditModeUI() {
        editActionsView.isHidden = false
        updateEditActionsViewConstraints()
        
        listContainerView.snp.remakeConstraints { make in
            make.top.left.right.equalToSuperview()
            if isEditingMode {
                make.bottom.equalToSuperview().offset(-editActionsContentHeight)
            } else {
                make.bottom.equalToSuperview()
            }
        }
        
        UIView.animate(withDuration: 0.3) {
            self.view.layoutIfNeeded()
        } completion: { _ in
            if !self.isEditingMode {
                self.editActionsView.isHidden = true
            }
        }
    }
    
    private func notifyChildViewControllersEditModeChanged() {
        listContainerView.setEditingMode(isEditingMode)
    }
    
    // MARK: - Action Handlers
    
    @objc private func selectAllItems(_ sender: UIButton) {
        listContainerView.setSelectAll(!sender.isSelected)
    }
    
    @objc private func deleteSelectedItems() {
        // 1. 获取所有选中视频的 videoId
        let selectedIds = listContainerView.getSelectedVideoIds()
        guard !selectedIds.isEmpty else {
            self.showToast("请选择要删除的内容")
            return
        }

        // 2. 弹出确认弹窗
        let alert = CommonAlertView(
            title: "确认取消",
            message: "确定后将取消点赞，此操作不可撤销",
            leftButtonTitle: "取消",
            rightButtonTitle: "确认"
        )
        alert.onLeftButtonTap = { [weak alert] in
            alert?.dismiss()
        }
        alert.onRightButtonTap = { [weak self, weak alert] in
            alert?.dismiss()
            self?.performDeletion(selectedIds: selectedIds)
        }
        alert.show()
    }

    private func performDeletion(selectedIds: [String]) {
        // 调用批量取消点赞API
        self.showLoading()
        APIManager.shared.doWorksLikeAndCollect(operateValue: 2, operateType: 1, worksIds: selectedIds) { [weak self] result in
            DispatchQueue.main.async {
                self?.hideLoading()
                switch result {
                case .success:
                    // 1. 本地移除已选中项
                    self?.listContainerView.deleteSelectedItems { updatedVideos in
                        self?.likedVideos = updatedVideos

                        // 若全部删除完，立即显示空占位并禁用管理按钮
                        let totalItems = updatedVideos.flatMap { $0 }.count
                        self?.emptyStateView.isHidden = totalItems > 0
                        self?.navigationItem.rightBarButtonItem?.isEnabled = totalItems > 0
                    }

                    // 2. 退出编辑模式，重置UI
                    self?.isEditingMode = false
                    self?.rightNavTitle = "管理"
                    self?.rightNavButtonTintColor = UIColor(hex: "#333333")

                    // 3. 再次请求数据，确保与服务端同步（重置刷新）
                    self?.requestLikedVideos(reset: true)
                case .failure(let error):
                    self?.showToast(error.localizedDescription)
                }
            }
        }
    }
    
    // MARK: - Loading
    private func showLoading() {
        MBProgressHUD.showAdded(to: self.view, animated: true)
    }
    private func hideLoading() {
        MBProgressHUD.hide(for: self.view, animated: true)
    }
    
    private func updateEditActionsViewConstraints() {
        let totalHeight = editActionsContentHeight + WindowUtil.safeAreaBottom
        editActionsView.snp.remakeConstraints { make in
            make.left.right.equalToSuperview()
            make.height.equalTo(totalHeight)
            make.bottom.equalToSuperview() // 贴住屏幕底部，覆盖安全区
        }
    }

    // MARK: - 视频播放

    private func playVideo(sectionIndex: Int, itemIndex: Int) {
        guard sectionIndex < likedVideos.count, itemIndex < likedVideos[sectionIndex].count else {
            print("LikedWorksViewController: 无效的视频索引 section=\(sectionIndex), item=\(itemIndex)")
            return
        }

        let likedItem = likedVideos[sectionIndex][itemIndex]

        // 将 LikedVideoItem 转换为 VideoItem
        var videoItem = VideoItem()
        videoItem.id = Int(likedItem.videoId)
        videoItem.worksTitle = likedItem.title
        videoItem.worksDescribe = likedItem.description
        videoItem.worksCoverImg = likedItem.image
        videoItem.likeNumber = likedItem.likeCount

        print("LikedWorksViewController: 播放点赞视频 - \(likedItem.title), ID: \(likedItem.videoId)")

        // 创建视频播放控制器
        let playerVC = VideoDisplayCenterViewController(
            singleVideoItem: videoItem,
            hideNavBackButton: false,
            showCustomNavBar: true,
            needsTabBarOffset: false
        )
        playerVC.hidesBottomBarWhenPushed = true

        if let nav = self.navigationController {
            nav.pushViewController(playerVC, animated: true)
        } else if let root = UIApplication.shared.windows.first(where: { $0.isKeyWindow })?.rootViewController {
            root.present(playerVC, animated: true)
        }
    }
}

// MARK: - LikedVideoItem 模型
struct LikedVideoItem {
    let image: String
    let likeCount: Int
    var isSelected: Bool = false
    let id = UUID()
    let date: Date? // 可选
    let videoId: String
    let title: String
    let description: String
    let isValid: Bool // 新增字段
}

// MARK: - LikedVideosListView
class LikedVideosListView: UIView {
    
    // MARK: - 属性
    
    private var currentAlertView: CleanupConfirmAlertView?
    private var dimmingView: UIView?
    
    // 下拉刷新和上拉加载回调
    var onPullToRefresh: (() -> Void)?
    var onLoadMore: (() -> Void)?
    
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .grouped)
        tableView.delegate = self
        tableView.dataSource = self
        tableView.backgroundColor = UIColor(hex: "#F5F5F5")
        tableView.separatorStyle = .none
        tableView.showsVerticalScrollIndicator = false
        tableView.register(LikedVideoCell.self, forCellReuseIdentifier: "LikedVideoCell")
        tableView.register(CollectionSectionHeaderView.self, forHeaderFooterViewReuseIdentifier: "CollectionSectionHeaderView")
        tableView.contentInset = UIEdgeInsets(top: 10, left: 0, bottom: 20, right: 0)
        // 下拉刷新
        let refresh = UIRefreshControl()
        refresh.addTarget(self, action: #selector(handlePullToRefresh), for: .valueChanged)
        tableView.refreshControl = refresh
        return tableView
    }()
    
    private var sectionTitles: [String]
    private var videos: [[LikedVideoItem]]
    private var onDataUpdate: (([[LikedVideoItem]]) -> Void)?
    var onSelectionChange: ((_ hasSelection: Bool, _ allSelected: Bool) -> Void)?
    var onVideoPlay: ((_ sectionIndex: Int, _ itemIndex: Int) -> Void)?
    private var isEditing: Bool = false
    
    // MARK: - 初始化
    
    init(sectionTitles: [String], videos: [[LikedVideoItem]], onDataUpdate: (([[LikedVideoItem]]) -> Void)? = nil) {
        self.sectionTitles = sectionTitles
        self.videos = videos
        self.onDataUpdate = onDataUpdate
        super.init(frame: .zero)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI 设置
    
    private func setupUI() {
        addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
//        let footerView = CleanupFooterView(frame: CGRect(x: 0, y: 0, width: bounds.width, height: 44))
//        footerView.onCleanupTapped = { [weak self] in
//            self?.showCleanupConfirmAlert()
//        }
//        tableView.tableFooterView = footerView
    }
    
    // MARK: - 公共方法
    
    func setEditingMode(_ isEditing: Bool) {
        self.isEditing = isEditing
        if !isEditing {
            deselectAllItemsAndUpdateState()
        } else {
            updateAndNotifySelectionState()
        }
        tableView.reloadData()
    }
    
    func setSelectAll(_ selectAll: Bool) {
        var didChange = false
        for i in 0..<videos.count {
            for j in 0..<videos[i].count {
                if videos[i][j].isSelected != selectAll {
                    videos[i][j].isSelected = selectAll
                    didChange = true
                }
            }
        }
        
        if didChange {
            tableView.reloadData()
            updateAndNotifySelectionState()
            onDataUpdate?(videos)
        } else {
            updateAndNotifySelectionState()
        }
    }
    
    func deleteSelectedItems(completion: @escaping ([[LikedVideoItem]]) -> Void) {
        guard isEditing else { return }
        
        var updatedVideos = [[LikedVideoItem]]()
        var didDeleteItems = false
        
        for sectionVideos in videos {
            let remainingVideos = sectionVideos.filter {
                if $0.isSelected {
                    didDeleteItems = true
                    return false
                }
                return true
            }
            updatedVideos.append(remainingVideos)
        }
        
        guard didDeleteItems else { return }
        
        self.videos = updatedVideos
        onDataUpdate?(updatedVideos)
        tableView.reloadData()
        updateAndNotifySelectionState()
        completion(updatedVideos)
    }
    
    // MARK: - 私有方法
    
    private func deselectAllItemsAndUpdateState() {
        var didChange = false
        for i in 0..<videos.count {
            for j in 0..<videos[i].count {
                if videos[i][j].isSelected {
                    videos[i][j].isSelected = false
                    didChange = true
                }
            }
        }
        
        if didChange {
            tableView.reloadData()
        }
        updateAndNotifySelectionState()
    }
    
    private func calculateSelectionState() -> (hasSelection: Bool, allSelected: Bool) {
        var hasSelection = false
        var allSelected = true
        var totalItems = 0
        
        for sectionVideos in videos {
            totalItems += sectionVideos.count
            for video in sectionVideos {
                if video.isSelected {
                    hasSelection = true
                } else {
                    allSelected = false
                }
            }
        }
        
        if totalItems == 0 {
            hasSelection = false
            allSelected = false
        }
        
        return (hasSelection, totalItems > 0 && allSelected)
    }
    
    private func updateAndNotifySelectionState() {
        guard isEditing else { return }
        let state = calculateSelectionState()
        onSelectionChange?(state.hasSelection, state.allSelected)
    }
    
    // MARK: - Alert Presentation
    
    private func showCleanupConfirmAlert() {
        guard let window = (UIApplication.shared.connectedScenes.first as? UIWindowScene)?.windows.first(where: { $0.isKeyWindow }) else {
            return
        }
        
        dimmingView = UIView(frame: window.bounds)
        dimmingView!.backgroundColor = UIColor.black.withAlphaComponent(0.4)
        dimmingView!.alpha = 0
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(dismissCleanupConfirmAlert))
        dimmingView!.addGestureRecognizer(tapGesture)
        dimmingView!.isUserInteractionEnabled = true
        window.addSubview(dimmingView!)
        
        currentAlertView = CleanupConfirmAlertView()
        currentAlertView!.alpha = 0
        currentAlertView!.transform = CGAffineTransform(scaleX: 1.1, y: 1.1)
        window.addSubview(currentAlertView!)
        currentAlertView!.snp.makeConstraints { make in
            make.edges.equalTo(window)
        }
        
        currentAlertView!.onCancel = { [weak self] in
            self?.dismissCleanupConfirmAlert()
        }
        
        currentAlertView!.onConfirm = { [weak self] in
            self?.dismissCleanupConfirmAlert()
        }
        
        UIView.animate(withDuration: 0.3) {
            self.dimmingView?.alpha = 1
            self.currentAlertView?.alpha = 1
            self.currentAlertView?.transform = .identity
        }
    }
    
    @objc private func dismissCleanupConfirmAlert() {
        UIView.animate(withDuration: 0.2) {
            self.dimmingView?.alpha = 0
            self.currentAlertView?.alpha = 0
            self.currentAlertView?.transform = CGAffineTransform(scaleX: 0.9, y: 0.9)
        } completion: { [weak self] _ in
            self?.dimmingView?.removeFromSuperview()
            self?.currentAlertView?.removeFromSuperview()
            self?.dimmingView = nil
            self?.currentAlertView = nil
        }
    }
    
    // 添加更新数据的方法
    func updateData(videos: [[LikedVideoItem]], sectionTitles: [String]) {
        self.videos = videos
        self.sectionTitles = sectionTitles
        tableView.reloadData()
    }
    
    // 新增：获取所有被选中的 videoId
    func getSelectedVideoIds() -> [String] {
        var ids: [String] = []
        for (sectionIndex, section) in videos.enumerated() {
            for (itemIndex, item) in section.enumerated() {
                print("section \(sectionIndex) item \(itemIndex) isSelected: \(item.isSelected), videoId: \(item.videoId)")
                if item.isSelected && !item.videoId.isEmpty {
                    ids.append(item.videoId)
                }
            }
        }
        return ids
    }
    
    @objc private func handlePullToRefresh() {
        onPullToRefresh?()
    }
    
    func endRefreshing() {
        tableView.refreshControl?.endRefreshing()
    }
    
    public var mainTableView: UITableView { tableView }
}

// MARK: - UITableViewDelegate, UITableViewDataSource
extension LikedVideosListView: UITableViewDelegate, UITableViewDataSource {
    
    func numberOfSections(in tableView: UITableView) -> Int {
        return videos.count
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return videos[section].isEmpty ? 0 : 1
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "LikedVideoCell", for: indexPath) as! LikedVideoCell
        let items = videos[indexPath.section]
        cell.configure(with: items, isEditing: isEditing, onItemSelect: { [weak self] itemIndex in
            self?.toggleItemSelection(at: indexPath, itemIndex: itemIndex)
        }, onItemPlay: { [weak self] itemIndex in
            self?.onVideoPlay?(indexPath.section, itemIndex)
        })
        return cell
    }
    
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        guard !videos[section].isEmpty else { return nil }
        
        let headerView = tableView.dequeueReusableHeaderFooterView(withIdentifier: "CollectionSectionHeaderView") as! CollectionSectionHeaderView
        if section < sectionTitles.count {
            headerView.configure(with: sectionTitles[section])
        }
        return headerView
    }
    
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return videos[section].isEmpty ? 0.1 : 40
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        guard !videos[indexPath.section].isEmpty else { return 0 }
        
        let items = videos[indexPath.section]
        let itemWidth = (UIScreen.main.bounds.width - 48) / 3
        let itemHeight = itemWidth * 1.5
        let rows = ceil(Double(items.count) / 3.0)
        let totalHeight = CGFloat(rows) * itemHeight + max(0, CGFloat(rows - 1)) * 8
        return totalHeight
    }
    
    private func toggleItemSelection(at indexPath: IndexPath, itemIndex: Int) {
        guard isEditing else { return }
        guard indexPath.section < videos.count, itemIndex < videos[indexPath.section].count else { return }
        
        videos[indexPath.section][itemIndex].isSelected.toggle()
        print("toggleItemSelection: section=\(indexPath.section), item=\(itemIndex), isSelected=\(videos[indexPath.section][itemIndex].isSelected)")
        
        if let cell = tableView.cellForRow(at: indexPath) as? LikedVideoCell {
            cell.updateItemSelectionState(at: itemIndex, isSelected: videos[indexPath.section][itemIndex].isSelected)
        }
        
        updateAndNotifySelectionState()
        onDataUpdate?(videos)
    }
    
    func tableView(_ tableView: UITableView, willDisplay cell: UITableViewCell, forRowAt indexPath: IndexPath) {
        // 上拉加载更多
        if indexPath.section == videos.count - 1 && indexPath.row == 0 {
            onLoadMore?()
        }
    }
}

// MARK: - LikedVideoCell
class LikedVideoCell: UITableViewCell {
    
    private lazy var collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        let itemWidth = (UIScreen.main.bounds.width - 48) / 3
        layout.itemSize = CGSize(width: itemWidth, height: itemWidth * 1.5)
        layout.minimumLineSpacing = 8
        layout.minimumInteritemSpacing = 8
        
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.backgroundColor = .clear
        collectionView.showsVerticalScrollIndicator = false
        collectionView.register(SmallVideoItemCell.self, forCellWithReuseIdentifier: "SmallVideoItemCell")
        collectionView.contentInset = UIEdgeInsets(top: 0, left: 16, bottom: 0, right: 16)
        collectionView.isScrollEnabled = false
        
        return collectionView
    }()
    
    private var items: [LikedVideoItem] = []
    private var isCellEditing: Bool = false
    private var onItemSelect: ((Int) -> Void)?
    private var onItemPlay: ((Int) -> Void)?
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        contentView.addSubview(collectionView)
        collectionView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    func configure(with items: [LikedVideoItem], isEditing: Bool, onItemSelect: ((Int) -> Void)?, onItemPlay: ((Int) -> Void)? = nil) {
        self.items = items
        self.isCellEditing = isEditing
        self.onItemSelect = onItemSelect
        self.onItemPlay = onItemPlay
        collectionView.reloadData()
    }
    
    func updateItemSelectionState(at itemIndex: Int, isSelected: Bool) {
        guard itemIndex < items.count else { return }
        if let cell = collectionView.cellForItem(at: IndexPath(item: itemIndex, section: 0)) as? SmallVideoItemCell {
            cell.updateSelectionIndicator(isEditing: isCellEditing, isSelected: isSelected)
        }
    }
}

// MARK: - UICollectionViewDelegate, UICollectionViewDataSource
extension LikedVideoCell: UICollectionViewDelegate, UICollectionViewDataSource {
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return items.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "SmallVideoItemCell", for: indexPath) as! SmallVideoItemCell
        let item = items[indexPath.item]
        cell.configure(imageURL: item.image, likeCount: item.likeCount, isLiked: true, isEditing: isCellEditing, isSelected: item.isSelected)
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        if isCellEditing {
            onItemSelect?(indexPath.item)
        } else {
            print("非编辑模式下，点击了点赞视频: \(indexPath.item)")
            onItemPlay?(indexPath.item)
        }
    }
}

// MARK: - EmptyStateView
class EmptyStateView: UIView {
    
    // MARK: - UI 组件
    
    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()
    
    private lazy var imageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.textAlignment = .center
        label.font = UIFont.systemFont(ofSize: 16, weight: .medium)
        label.textColor = UIColor(hex: "#333333")
        return label
    }()
    
    private lazy var subtitleLabel: UILabel = {
        let label = UILabel()
        label.textAlignment = .center
        label.font = UIFont.systemFont(ofSize: 14)
        label.textColor = UIColor(hex: "#999999")
        label.numberOfLines = 0
        return label
    }()
    
    // MARK: - 初始化
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - UI 设置
    
    private func setupUI() {
        backgroundColor = UIColor(hex: "#F5F5F5")
        
        addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.8)
        }
        
        containerView.addSubview(imageView)
        imageView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.centerX.equalToSuperview()
            make.size.equalTo(CGSize(width: 120, height: 120))
        }
        
        containerView.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(imageView.snp.bottom).offset(16)
            make.centerX.equalToSuperview()
            make.left.right.equalToSuperview()
        }
        
        containerView.addSubview(subtitleLabel)
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.centerX.equalToSuperview()
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview()
        }
    }
    
    // MARK: - 公共方法
    
    func configure(image: UIImage, title: String, subtitle: String) {
        imageView.image = image
        titleLabel.text = title
        subtitleLabel.text = subtitle
    }
}
